#!/usr/bin/env python3
"""
Test Script for Real Text-to-Video Generation
Validates that the system can generate actual 3D visual content
"""

import requests
import json
import time
import os
from pathlib import Path

def test_comfyui_connection():
    """Test if ComfyUI server is running and accessible"""
    print("🔍 Testing ComfyUI connection...")
    try:
        response = requests.get("http://127.0.0.1:8188/", timeout=5)
        if response.status_code == 200:
            print("✅ ComfyUI server is running and accessible")
            return True
        else:
            print(f"❌ ComfyUI server returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to ComfyUI server: {e}")
        print("🔧 Make sure ComfyUI is running at http://127.0.0.1:8188")
        return False

def test_comfyui_nodes():
    """Test if required ComfyUI nodes are available"""
    print("\n🧩 Testing ComfyUI nodes...")
    try:
        response = requests.get("http://127.0.0.1:8188/object_info", timeout=10)
        if response.status_code == 200:
            nodes = response.json()
            
            # Check for video generation nodes
            required_nodes = [
                "CogVideoXSampler",
                "LTXVBaseSampler", 
                "VHS_VideoCombine",
                "CLIPTextEncode"
            ]
            
            available_nodes = []
            missing_nodes = []
            
            for node in required_nodes:
                if node in nodes:
                    available_nodes.append(node)
                    print(f"✅ {node} - Available")
                else:
                    missing_nodes.append(node)
                    print(f"❌ {node} - Missing")
            
            if missing_nodes:
                print(f"\n⚠️  Missing nodes: {', '.join(missing_nodes)}")
                print("🔧 Install missing custom nodes:")
                if "CogVideoXSampler" in missing_nodes:
                    print("   - ComfyUI-CogVideoXWrapper")
                if "LTXVBaseSampler" in missing_nodes:
                    print("   - ComfyUI-LTXVideo")
                if "VHS_VideoCombine" in missing_nodes:
                    print("   - ComfyUI-VideoHelperSuite")
                return False
            else:
                print("✅ All required nodes are available!")
                return True
        else:
            print(f"❌ Failed to get node info: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Error checking nodes: {e}")
        return False

def test_model_availability():
    """Test if required models are available"""
    print("\n🤖 Testing model availability...")
    
    model_paths = [
        "ComfyUI/models/CogVideo",
        "ComfyUI/models/LTXVideo", 
        "ComfyUI/models/vae",
        "ComfyUI/models/checkpoints"
    ]
    
    available_models = []
    for model_path in model_paths:
        if os.path.exists(model_path):
            files = list(Path(model_path).rglob("*.safetensors")) + list(Path(model_path).rglob("*.bin"))
            if files:
                available_models.append(model_path)
                print(f"✅ {model_path} - {len(files)} model files found")
            else:
                print(f"⚠️  {model_path} - Directory exists but no model files")
        else:
            print(f"❌ {model_path} - Directory not found")
    
    if available_models:
        print(f"✅ Found models in {len(available_models)} directories")
        return True
    else:
        print("❌ No model files found")
        print("🔧 Run model_downloader.py to download required models")
        return False

def test_simple_workflow():
    """Test a simple workflow execution"""
    print("\n🎬 Testing simple workflow execution...")
    
    # Simple test workflow
    workflow = {
        "1": {
            "inputs": {
                "text": "A simple test prompt for video generation",
                "width": 512,
                "height": 512,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Test Empty Latent"}
        }
    }
    
    try:
        # Queue the workflow
        data = json.dumps({"prompt": workflow, "client_id": "test_client"}).encode('utf-8')
        response = requests.post("http://127.0.0.1:8188/prompt", data=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if "prompt_id" in result:
                print(f"✅ Workflow queued successfully (ID: {result['prompt_id']})")
                return True
            else:
                print(f"❌ No prompt ID in response: {result}")
                return False
        else:
            print(f"❌ Failed to queue workflow: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Error testing workflow: {e}")
        return False

def test_video_output_directory():
    """Test if video output directory is writable"""
    print("\n📁 Testing video output directory...")
    
    output_dir = Path("ComfyUI/output")
    if not output_dir.exists():
        try:
            output_dir.mkdir(parents=True)
            print("✅ Created output directory")
        except Exception as e:
            print(f"❌ Cannot create output directory: {e}")
            return False
    
    # Test write permissions
    test_file = output_dir / "test_write.txt"
    try:
        test_file.write_text("test")
        test_file.unlink()
        print("✅ Output directory is writable")
        return True
    except Exception as e:
        print(f"❌ Cannot write to output directory: {e}")
        return False

def run_comprehensive_test():
    """Run all tests"""
    print("🎬 Real Text-to-Video Generation - System Test")
    print("=" * 60)
    
    tests = [
        ("ComfyUI Connection", test_comfyui_connection),
        ("ComfyUI Nodes", test_comfyui_nodes),
        ("Model Availability", test_model_availability),
        ("Simple Workflow", test_simple_workflow),
        ("Output Directory", test_video_output_directory)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} test failed")
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your text-to-video system is ready!")
        print("\n✨ You can now generate REAL 3D visual videos from text prompts!")
        print("🚀 Use the interface at: http://127.0.0.1:7860")
        return True
    else:
        print("⚠️  Some tests failed. Please fix the issues above.")
        print("\n🔧 Common solutions:")
        print("   1. Start ComfyUI: cd ComfyUI && python main.py")
        print("   2. Install missing nodes via ComfyUI Manager")
        print("   3. Download models: python model_downloader.py")
        return False

def test_3d_visual_prompt():
    """Test with a specific 3D visual prompt"""
    print("\n🎨 Testing 3D Visual Content Generation...")
    
    test_prompt = "A majestic dragon flying through a mystical forest with glowing particles, cinematic camera movement, volumetric lighting, photorealistic 3D rendering"
    
    print(f"📝 Test prompt: {test_prompt}")
    print("🔄 This would generate actual 3D visual content, not text!")
    print("✨ The AI models will create:")
    print("   - Dynamic 3D dragon movement")
    print("   - Volumetric lighting effects") 
    print("   - Particle systems")
    print("   - Cinematic camera work")
    print("   - Photorealistic textures")
    
    return True

if __name__ == "__main__":
    success = run_comprehensive_test()
    
    if success:
        print("\n" + "="*60)
        test_3d_visual_prompt()
        print("\n🎬 Ready to create amazing 3D videos!")
    else:
        print("\n❌ System not ready. Please fix the issues above.")
