#!/usr/bin/env python3
"""
Automated Model Downloader for Text-to-Video Generation
Downloads required models for CogVideoX and LTXVideo
"""

import os
import requests
import json
from pathlib import Path
import subprocess
import sys

class ModelDownloader:
    def __init__(self, comfyui_path="ComfyUI"):
        self.comfyui_path = Path(comfyui_path)
        self.models_path = self.comfyui_path / "models"
        
        # Model configurations
        self.models = {
            "cogvideox": {
                "name": "CogVideoX-5B",
                "url": "https://huggingface.co/THUDM/CogVideoX-5b",
                "local_path": self.models_path / "CogVideo" / "CogVideoX-5b",
                "files": [
                    "model_index.json",
                    "scheduler/scheduler_config.json",
                    "text_encoder/config.json",
                    "text_encoder/pytorch_model.bin",
                    "transformer/config.json",
                    "transformer/diffusion_pytorch_model.safetensors",
                    "vae/config.json",
                    "vae/diffusion_pytorch_model.safetensors"
                ],
                "size": "~9.5GB",
                "description": "High-quality text-to-video generation model"
            },
            "ltxvideo": {
                "name": "LTX-Video",
                "url": "https://huggingface.co/Lightricks/LTX-Video",
                "local_path": self.models_path / "LTXVideo" / "LTX-Video",
                "files": [
                    "model_index.json",
                    "scheduler/scheduler_config.json",
                    "text_encoder/config.json",
                    "text_encoder/model.safetensors",
                    "transformer/config.json",
                    "transformer/diffusion_pytorch_model.safetensors",
                    "vae/config.json",
                    "vae/diffusion_pytorch_model.safetensors"
                ],
                "size": "~8.2GB",
                "description": "Fast and efficient text-to-video generation"
            },
            "video_vae": {
                "name": "Video VAE",
                "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse",
                "local_path": self.models_path / "vae" / "sd-vae-ft-mse",
                "files": [
                    "config.json",
                    "diffusion_pytorch_model.safetensors"
                ],
                "size": "~335MB",
                "description": "Video Variational Autoencoder for encoding/decoding"
            }
        }
    
    def check_git_lfs(self):
        """Check if git-lfs is installed"""
        try:
            result = subprocess.run(["git", "lfs", "version"], capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def install_git_lfs(self):
        """Install git-lfs if not available"""
        print("📦 Installing git-lfs...")
        try:
            # Try to install via brew on macOS
            subprocess.run(["brew", "install", "git-lfs"], check=True)
            subprocess.run(["git", "lfs", "install"], check=True)
            print("✅ git-lfs installed successfully")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Failed to install git-lfs automatically")
            print("🔧 Please install git-lfs manually:")
            print("   macOS: brew install git-lfs")
            print("   Ubuntu: sudo apt install git-lfs")
            print("   Then run: git lfs install")
            return False
    
    def check_huggingface_cli(self):
        """Check if huggingface-hub CLI is available"""
        try:
            result = subprocess.run(["huggingface-cli", "--version"], capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def install_huggingface_cli(self):
        """Install huggingface-hub CLI"""
        print("📦 Installing huggingface-hub...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "huggingface-hub[cli]"], check=True)
            print("✅ huggingface-hub installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install huggingface-hub")
            return False
    
    def check_model_exists(self, model_key):
        """Check if a model is already downloaded"""
        model_info = self.models[model_key]
        local_path = model_info["local_path"]
        
        if not local_path.exists():
            return False
        
        # Check if key files exist
        for file_path in model_info["files"][:3]:  # Check first 3 files
            if not (local_path / file_path).exists():
                return False
        
        return True
    
    def download_model(self, model_key):
        """Download a specific model"""
        model_info = self.models[model_key]
        
        print(f"\n🎬 Downloading {model_info['name']}...")
        print(f"📁 Size: {model_info['size']}")
        print(f"📝 Description: {model_info['description']}")
        print(f"🔗 Source: {model_info['url']}")
        
        # Create directory
        model_info["local_path"].mkdir(parents=True, exist_ok=True)
        
        try:
            # Use huggingface-cli to download
            cmd = [
                "huggingface-cli",
                "download",
                model_info["url"].split("/")[-2] + "/" + model_info["url"].split("/")[-1],
                "--local-dir", str(model_info["local_path"]),
                "--local-dir-use-symlinks", "False"
            ]
            
            print(f"🔄 Running: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            
            print(f"✅ {model_info['name']} downloaded successfully!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to download {model_info['name']}")
            print(f"Error: {e.stderr}")
            
            # Fallback: try git clone
            print("🔄 Trying alternative download method...")
            try:
                cmd = ["git", "clone", model_info["url"], str(model_info["local_path"])]
                subprocess.run(cmd, check=True)
                print(f"✅ {model_info['name']} downloaded via git!")
                return True
            except subprocess.CalledProcessError:
                print(f"❌ All download methods failed for {model_info['name']}")
                return False
    
    def setup_models(self):
        """Set up all required models"""
        print("🎬 Setting up Text-to-Video AI Models...")
        print("=" * 50)
        
        # Check prerequisites
        if not self.check_git_lfs():
            if not self.install_git_lfs():
                return False
        
        if not self.check_huggingface_cli():
            if not self.install_huggingface_cli():
                return False
        
        # Create model directories
        for model_key, model_info in self.models.items():
            model_info["local_path"].parent.mkdir(parents=True, exist_ok=True)
        
        # Check and download models
        models_to_download = []
        for model_key, model_info in self.models.items():
            if self.check_model_exists(model_key):
                print(f"✅ {model_info['name']} already exists")
            else:
                models_to_download.append(model_key)
        
        if not models_to_download:
            print("\n🎉 All models are already downloaded!")
            return True
        
        print(f"\n📥 Need to download {len(models_to_download)} models:")
        total_size = sum(float(self.models[key]['size'].split('~')[1].split('GB')[0]) 
                        for key in models_to_download if 'GB' in self.models[key]['size'])
        print(f"📊 Total download size: ~{total_size:.1f}GB")
        
        # Ask for confirmation
        response = input("\n❓ Continue with download? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ Download cancelled")
            return False
        
        # Download models
        success_count = 0
        for model_key in models_to_download:
            if self.download_model(model_key):
                success_count += 1
        
        print(f"\n🎉 Downloaded {success_count}/{len(models_to_download)} models successfully!")
        
        if success_count == len(models_to_download):
            print("✅ All models ready for text-to-video generation!")
            return True
        else:
            print("⚠️ Some models failed to download. Video generation may not work properly.")
            return False
    
    def list_models(self):
        """List all available models and their status"""
        print("🎬 Text-to-Video AI Models Status:")
        print("=" * 50)
        
        for model_key, model_info in self.models.items():
            status = "✅ Downloaded" if self.check_model_exists(model_key) else "❌ Not found"
            print(f"{model_info['name']:<20} | {status:<15} | {model_info['size']}")
            print(f"  📝 {model_info['description']}")
            print(f"  📁 {model_info['local_path']}")
            print()

if __name__ == "__main__":
    downloader = ModelDownloader()
    
    if len(sys.argv) > 1 and sys.argv[1] == "list":
        downloader.list_models()
    else:
        downloader.setup_models()
