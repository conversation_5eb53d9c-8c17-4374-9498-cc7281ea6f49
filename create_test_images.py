#!/usr/bin/env python3
"""
Create test images for video generation
"""

import os
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def create_test_images():
    """Create a series of test images for video generation"""
    
    # Create input directory
    input_dir = "ComfyUI/input"
    os.makedirs(input_dir, exist_ok=True)
    
    # Image settings
    width, height = 512, 512
    num_frames = 16
    
    print(f"Creating {num_frames} test images...")
    
    for i in range(num_frames):
        # Create a new image with gradient background
        img = Image.new('RGB', (width, height))
        draw = ImageDraw.Draw(img)
        
        # Create animated gradient
        for y in range(height):
            # Animated color based on frame and position
            r = int(128 + 127 * np.sin((i * 0.1) + (y * 0.01)))
            g = int(128 + 127 * np.sin((i * 0.15) + (y * 0.01) + 2))
            b = int(128 + 127 * np.sin((i * 0.2) + (y * 0.01) + 4))
            
            color = (max(0, min(255, r)), max(0, min(255, g)), max(0, min(255, b)))
            draw.line([(0, y), (width, y)], fill=color)
        
        # Add animated circle
        circle_x = int(width/2 + 100 * np.sin(i * 0.3))
        circle_y = int(height/2 + 50 * np.cos(i * 0.3))
        circle_radius = int(30 + 20 * np.sin(i * 0.5))
        
        draw.ellipse([
            circle_x - circle_radius, 
            circle_y - circle_radius,
            circle_x + circle_radius, 
            circle_y + circle_radius
        ], fill=(255, 255, 255), outline=(0, 0, 0), width=3)
        
        # Add frame number
        try:
            # Try to use a default font
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 36)
        except:
            # Fallback to default font
            font = ImageFont.load_default()
        
        text = f"Frame {i+1:02d}"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        text_x = (width - text_width) // 2
        text_y = height - text_height - 20
        
        # Draw text with outline
        for dx in [-2, -1, 0, 1, 2]:
            for dy in [-2, -1, 0, 1, 2]:
                if dx != 0 or dy != 0:
                    draw.text((text_x + dx, text_y + dy), text, font=font, fill=(0, 0, 0))
        draw.text((text_x, text_y), text, font=font, fill=(255, 255, 255))
        
        # Add animated text
        title = "3D Visual Test"
        title_bbox = draw.textbbox((0, 0), title, font=font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (width - title_width) // 2
        title_y = 30
        
        # Animated title color
        title_r = int(255 * (0.5 + 0.5 * np.sin(i * 0.4)))
        title_g = int(255 * (0.5 + 0.5 * np.sin(i * 0.4 + 2)))
        title_b = int(255 * (0.5 + 0.5 * np.sin(i * 0.4 + 4)))
        title_color = (title_r, title_g, title_b)
        
        # Draw title with outline
        for dx in [-2, -1, 0, 1, 2]:
            for dy in [-2, -1, 0, 1, 2]:
                if dx != 0 or dy != 0:
                    draw.text((title_x + dx, title_y + dy), title, font=font, fill=(0, 0, 0))
        draw.text((title_x, title_y), title, font=font, fill=title_color)
        
        # Save the image
        filename = f"test_frame_{i+1:03d}.png"
        filepath = os.path.join(input_dir, filename)
        img.save(filepath)
        print(f"Created: {filepath}")
    
    print(f"\n✅ Created {num_frames} test images in {input_dir}/")
    print("These images will be used to create a test video!")
    
    return input_dir

if __name__ == "__main__":
    create_test_images()
