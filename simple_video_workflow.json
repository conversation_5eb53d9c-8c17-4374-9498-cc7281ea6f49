{"1": {"inputs": {"width": 512, "height": 512, "batch_size": 16}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "2": {"inputs": {"samples": ["1", 0], "vae": ["3", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "3": {"inputs": {"vae_name": "vae-ft-mse-840000-ema-pruned.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "4": {"inputs": {"images": ["2", 0], "frame_rate": 8, "loop_count": 0, "filename_prefix": "test_video", "format": "video/h264-mp4", "pingpong": false, "save_output": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "5": {"inputs": {"text": "A beautiful sunset over mountains", "clip": ["6", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "6": {"inputs": {"clip_name": "clip-vit-large-patch14.safetensors"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "7": {"inputs": {"seed": 42, "steps": 20, "cfg": 7.0, "sampler_name": "euler", "scheduler": "normal", "denoise": 1.0, "model": ["8", 0], "positive": ["5", 0], "negative": ["9", 0], "latent_image": ["1", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "8": {"inputs": {"unet_name": "sd_xl_base_1.0.safetensors"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "9": {"inputs": {"text": "", "clip": ["6", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative)"}}}