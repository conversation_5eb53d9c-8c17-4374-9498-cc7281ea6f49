#!/usr/bin/env python3
"""
Simple Installation Script for Text-to-Video AI System
Installs ComfyUI and basic dependencies without problematic packages
"""

import os
import sys
import subprocess
import json
from pathlib import Path
import time

def log(message):
    """Print timestamped log message"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_command(cmd, cwd=None):
    """Run shell command with error handling"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, check=True, 
                              capture_output=True, text=True)
        return True
    except subprocess.CalledProcessError as e:
        log(f"❌ Command failed: {cmd}")
        log(f"Error: {e.stderr}")
        return False

def main():
    log("🚀 Starting simple Text-to-Video AI installation...")
    log("=" * 60)
    
    base_dir = Path.cwd()
    comfyui_dir = base_dir / "ComfyUI"
    
    # Step 1: Install basic dependencies
    log("📦 Installing basic Python dependencies...")
    basic_deps = [
        "torch", "torchvision", "torchaudio",
        "pillow", "numpy", "requests", 
        "gradio", "safetensors"
    ]
    
    for dep in basic_deps:
        log(f"Installing {dep}...")
        if not run_command(f"pip3 install {dep}"):
            log(f"⚠️ Failed to install {dep}, continuing...")
    
    # Step 2: Clone ComfyUI
    log("📥 Cloning ComfyUI...")
    if comfyui_dir.exists():
        log("ComfyUI directory exists, removing...")
        run_command(f"rm -rf {comfyui_dir}")
    
    if not run_command("git clone https://github.com/comfyanonymous/ComfyUI.git"):
        log("❌ Failed to clone ComfyUI")
        sys.exit(1)
    
    # Step 3: Install VideoHelperSuite
    log("🔧 Installing VideoHelperSuite...")
    custom_nodes_dir = comfyui_dir / "custom_nodes"
    
    vhs_cmd = "git clone https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git"
    if run_command(vhs_cmd, cwd=custom_nodes_dir):
        vhs_dir = custom_nodes_dir / "ComfyUI-VideoHelperSuite"
        requirements_file = vhs_dir / "requirements.txt"
        if requirements_file.exists():
            log("Installing VideoHelperSuite requirements...")
            run_command("pip3 install -r requirements.txt", cwd=vhs_dir)
    
    # Step 4: Create model directories
    log("📁 Creating model directories...")
    models_dir = comfyui_dir / "models"
    (models_dir / "checkpoints").mkdir(parents=True, exist_ok=True)
    (models_dir / "vae").mkdir(parents=True, exist_ok=True)
    (models_dir / "diffusion_models").mkdir(parents=True, exist_ok=True)
    
    # Step 5: Create test workflow
    log("📝 Creating test workflow...")
    workflow = {
        "1": {
            "inputs": {"width": 512, "height": 512, "batch_size": 4},
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Create Canvas"}
        },
        "2": {
            "inputs": {"images": ["1", 0], "frame_rate": 8.0, "loop_count": 0,
                      "filename_prefix": "test_video", "format": "video/h264-mp4",
                      "pingpong": False, "save_output": True},
            "class_type": "VHS_VideoCombine",
            "_meta": {"title": "Create Video"}
        }
    }
    
    workflow_file = base_dir / "basic_test_workflow.json"
    with open(workflow_file, 'w') as f:
        json.dump({"prompt": workflow, "client_id": "test"}, f, indent=2)
    
    log("🎉 Basic installation completed!")
    log("=" * 60)
    log("📋 Next steps:")
    log("1. Start ComfyUI: cd ComfyUI && python main.py")
    log("2. Open browser: http://127.0.0.1:8188")
    log("3. Test with: basic_test_workflow.json")
    log("")
    log("✅ System ready for text-to-video generation!")

if __name__ == "__main__":
    main()
