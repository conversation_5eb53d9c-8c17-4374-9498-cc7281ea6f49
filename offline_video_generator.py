#!/usr/bin/env python3
"""
Offline Text-to-Video Generator
Works completely offline without internet connection or external APIs
"""

import gradio as gr
import requests
import json
import time
import os
import uuid
from pathlib import Path

# Offline Configuration
COMFYUI_URL = "http://127.0.0.1:8188"
OUTPUT_DIR = "ComfyUI/output"

class OfflineComfyUIClient:
    def __init__(self, server_address="127.0.0.1:8188"):
        self.server_address = server_address
        self.client_id = str(uuid.uuid4())
        
    def queue_prompt(self, prompt):
        """Queue a prompt for execution"""
        try:
            p = {"prompt": prompt, "client_id": self.client_id}
            data = json.dumps(p).encode('utf-8')
            req = requests.post(f"http://{self.server_address}/prompt", data=data, timeout=30)
            return json.loads(req.content)
        except Exception as e:
            print(f"Error queuing prompt: {e}")
            return None

    def get_history(self, prompt_id):
        """Get the history of a prompt execution"""
        try:
            response = requests.get(f"http://{self.server_address}/history/{prompt_id}", timeout=30)
            return json.loads(response.content)
        except Exception as e:
            print(f"Error getting history: {e}")
            return None

    def check_server(self):
        """Check if ComfyUI server is running"""
        try:
            response = requests.get(f"http://{self.server_address}/", timeout=5)
            return response.status_code == 200
        except:
            return False

class OfflineVideoGenerator:
    def __init__(self):
        self.client = OfflineComfyUIClient()
        self.current_prompt_id = None
        
    def create_offline_workflow(self, prompt, fps=24, duration=3, style="cinematic"):
        """Create a workflow that works completely offline with available nodes"""
        
        # Enhanced prompt for 3D visual content
        enhanced_prompt = f"{prompt}, {style}, high quality, detailed, cinematic lighting, smooth motion, 3d rendered, volumetric lighting, realistic textures, dynamic camera movement"
        negative_prompt = "blurry, low quality, distorted, watermark, text, bad anatomy, static image, 2d flat, boring, simple"
        
        # Workflow using only available offline nodes
        workflow = {
            "1": {
                "inputs": {
                    "width": 768,
                    "height": 512,
                    "batch_size": fps * duration
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "Create Latent Canvas"}
            },
            "2": {
                "inputs": {
                    "text": enhanced_prompt,
                    "clip": ["4", 0]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "Encode Positive Prompt"}
            },
            "3": {
                "inputs": {
                    "text": negative_prompt,
                    "clip": ["4", 0]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "Encode Negative Prompt"}
            },
            "4": {
                "inputs": {
                    "ckpt_name": "sd_xl_base_1.0.safetensors"
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "Load AI Model"}
            },
            "5": {
                "inputs": {
                    "seed": int(time.time()),
                    "steps": 25,
                    "cfg": 7.5,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "positive": ["2", 0],
                    "negative": ["3", 0],
                    "latent_image": ["1", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "Generate Images"}
            },
            "6": {
                "inputs": {
                    "samples": ["5", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "Decode to Images"}
            },
            "7": {
                "inputs": {
                    "filename_prefix": f"offline_3d_video_{int(time.time())}",
                    "fps": fps,
                    "format": "video/h264-mp4",
                    "pix_fmt": "yuv420p",
                    "crf": 19,
                    "save_metadata": True,
                    "pingpong": False,
                    "save_output": True,
                    "images": ["6", 0]
                },
                "class_type": "VHS_VideoCombine",
                "_meta": {"title": "Create Video File"}
            }
        }
        
        return workflow

    def generate_video_offline(self, prompt, fps=24, duration=3, style="cinematic"):
        """Generate video completely offline"""
        
        if not prompt.strip():
            yield None, "⚠️ Please enter a prompt to generate a video."
            return
            
        # Check if ComfyUI server is running
        if not self.client.check_server():
            yield None, "❌ **ComfyUI Server Not Running**\n\n🔧 **Solution:** Start ComfyUI server first:\n\n1. Open Terminal\n2. Navigate to ComfyUI folder\n3. Run: `python main.py`\n4. Wait for server to start on http://127.0.0.1:8188\n\n🔒 **Offline Mode:** No internet connection required!"
            return
            
        try:
            yield None, f"🔒 **Starting OFFLINE video generation...**\n\n💭 **Your prompt:** {prompt}\n\n⚙️ **Settings:** {fps}fps, {duration}s, {style} style\n\n🔄 **Status:** Creating workflow..."
            
            # Create offline workflow
            workflow = self.create_offline_workflow(prompt, fps, duration, style)
            
            yield None, f"🔒 **OFFLINE video generation in progress...**\n\n💭 **Your prompt:** {prompt}\n\n⚙️ **Settings:** {fps}fps, {duration}s, {style} style\n\n🔄 **Status:** Sending to local ComfyUI..."
            
            # Queue the prompt
            result = self.client.queue_prompt(workflow)
            if not result:
                yield None, "❌ **Error:** Failed to queue prompt. Check ComfyUI server."
                return
                
            prompt_id = result.get('prompt_id')
            if not prompt_id:
                yield None, "❌ **Error:** No prompt ID returned from ComfyUI."
                return
                
            self.current_prompt_id = prompt_id
            
            yield None, f"🔒 **OFFLINE video generation running...**\n\n💭 **Your prompt:** {prompt}\n\n⚙️ **Settings:** {fps}fps, {duration}s, {style} style\n\n🔄 **Status:** AI models generating 3D visual content...\n\n📋 **Prompt ID:** {prompt_id}\n\n🌐 **Mode:** Completely offline - no internet required!"
            
            # Monitor progress
            start_time = time.time()
            max_wait_time = 180  # 3 minutes max
            
            while time.time() - start_time < max_wait_time:
                # Check if completed
                history = self.client.get_history(prompt_id)
                if history and prompt_id in history:
                    # Generation completed
                    outputs = history[prompt_id].get('outputs', {})
                    
                    # Look for video output
                    video_path = None
                    for node_id, output in outputs.items():
                        if 'gifs' in output and output['gifs']:
                            video_info = output['gifs'][0]
                            video_path = f"ComfyUI/output/{video_info['filename']}"
                            break
                        elif 'videos' in output and output['videos']:
                            video_info = output['videos'][0]
                            video_path = f"ComfyUI/output/{video_info['filename']}"
                            break
                    
                    if video_path and os.path.exists(video_path):
                        yield video_path, f"✅ **OFFLINE 3D Video Generated Successfully!**\n\n💭 **Your prompt:** {prompt}\n\n🎬 **Result:** 3D visual video created completely offline\n\n📁 **Location:** `{video_path}`\n\n🔒 **Offline Success:** Generated without internet connection!\n\n🎉 **Achievement:** Your prompt transformed into actual 3D visual content!"
                    else:
                        yield None, f"✅ **Generation completed offline!**\n\n💭 **Your prompt:** {prompt}\n\n🎬 **Result:** Video saved to ComfyUI output folder\n\n📁 **Check:** ComfyUI/output/ directory\n\n📋 **Prompt ID:** {prompt_id}\n\n🔒 **Offline Mode:** No internet required!"
                    return
                
                elapsed = int(time.time() - start_time)
                yield None, f"🔒 **OFFLINE video generation in progress...**\n\n💭 **Your prompt:** {prompt}\n\n⚙️ **Settings:** {fps}fps, {duration}s, {style} style\n\n🔄 **Status:** AI models working offline...\n\n⏱️ **Elapsed:** {elapsed}s\n\n📋 **Prompt ID:** {prompt_id}\n\n🌐 **Mode:** Completely offline!"
                
                time.sleep(3)
            
            # Timeout
            yield None, f"⏰ **Timeout:** Generation took longer than expected.\n\n💭 **Your prompt:** {prompt}\n\n🔍 **Check:** ComfyUI interface at http://127.0.0.1:8188\n\n📋 **Prompt ID:** {prompt_id}\n\n🔒 **Note:** Still running offline!"
                
        except Exception as e:
            yield None, f"❌ **Error:** {str(e)}\n\n🔧 **Solution:** Make sure ComfyUI is running at http://127.0.0.1:8188\n\n🔒 **Offline Mode:** No internet connection required"

def create_offline_interface():
    """Create the offline video generation interface"""
    
    generator = OfflineVideoGenerator()
    
    # Enhanced CSS for offline mode
    css = """
    .gradio-container {
        max-width: 1000px !important;
        margin: 0 auto !important;
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
        min-height: 100vh !important;
    }
    
    .main-container {
        background: white !important;
        border-radius: 16px !important;
        padding: 30px !important;
        margin: 20px !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
        border: 2px solid #10a37f !important;
    }
    
    .offline-badge {
        background: linear-gradient(135deg, #10a37f 0%, #0d8f6c 100%) !important;
        color: white !important;
        padding: 8px 16px !important;
        border-radius: 20px !important;
        font-weight: 600 !important;
        display: inline-block !important;
        margin: 10px 0 !important;
    }
    """
    
    with gr.Blocks(css=css, title="🔒 Offline AI Video Generator", theme=gr.themes.Soft()) as app:
        
        with gr.Column(elem_classes=["main-container"]):
            
            # Header with offline emphasis
            gr.HTML("""
            <div style="text-align: center; padding: 0 0 30px 0;">
                <h1 style="color: #2d3748; margin-bottom: 8px; font-size: 2.8em;">🔒 Offline AI Video Generator</h1>
                <p style="color: #718096; font-size: 20px; font-weight: 500;">Create 3D visual videos completely offline • No internet required</p>
                <div style="background: linear-gradient(135deg, #10a37f 0%, #0d8f6c 100%); color: white; padding: 12px 24px; border-radius: 25px; display: inline-block; margin-top: 15px; font-weight: 600; font-size: 16px;">
                    🔒 100% OFFLINE OPERATION • 🚫 NO EXTERNAL APIS • 🏠 LOCAL AI MODELS
                </div>
            </div>
            """)
            
            # Main prompt input
            prompt_input = gr.Textbox(
                placeholder="🎨 Describe the 3D visual video you want to create offline...\n\nExample: A majestic dragon flying through a mystical forest with glowing particles, cinematic camera movement, volumetric lighting, photorealistic 3D rendering",
                lines=5,
                elem_classes=["prompt-box"],
                show_label=False,
                max_lines=10
            )
            
            # Settings
            with gr.Row(elem_classes=["settings-row"]):
                fps_choice = gr.Radio(
                    choices=[16, 24, 30], 
                    value=24, 
                    label="🎞️ Frame Rate",
                    info="Higher = smoother motion"
                )
                duration_slider = gr.Slider(
                    minimum=2, 
                    maximum=6, 
                    value=3, 
                    step=1,
                    label="⏱️ Duration (seconds)",
                    info="Video length (offline optimized)"
                )
                style_choice = gr.Dropdown(
                    choices=[
                        "cinematic", 
                        "photorealistic", 
                        "3d rendered", 
                        "artistic",
                        "sci-fi",
                        "fantasy"
                    ],
                    value="cinematic",
                    label="🎨 Style",
                    info="Visual aesthetic"
                )
            
            # Generate button
            generate_btn = gr.Button(
                "🔒 Generate Video Offline", 
                variant="primary",
                elem_classes=["generate-btn"],
                size="lg"
            )
            
            # Video output
            video_output = gr.Video(
                label="🎬 Generated Video (Offline)",
                elem_classes=["video-output"],
                autoplay=True,
                show_share_button=False
            )
            
            # Status display
            status_display = gr.Markdown(
                "🔒 **Ready for offline video generation!**\n\nThis system works completely offline without internet connection. Enter your prompt and generate amazing 3D videos using local AI models.",
                elem_classes=["status-display"]
            )
        
        # Offline examples
        gr.HTML("""
        <div style="margin-top: 25px; padding: 30px; background: white; border-radius: 16px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: 2px solid #10a37f;">
            <h3 style="color: #2d3748; margin-bottom: 25px; text-align: center; font-size: 1.5em;">🔒 Offline 3D Visual Prompts</h3>
            <div style="display: grid; gap: 15px;">
                <div style="padding: 20px; background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%); color: white; border-radius: 12px; cursor: pointer;" onclick="document.querySelector('textarea').value='A majestic phoenix rising from flames in a mystical forest, 3d rendered, volumetric lighting, cinematic camera movement, photorealistic'">
                    🔥 Mystical phoenix with 3D flames and volumetric lighting
                </div>
                <div style="padding: 20px; background: linear-gradient(135deg, #10a37f 0%, #0d8f6c 100%); color: white; border-radius: 12px; cursor: pointer;" onclick="document.querySelector('textarea').value='Futuristic city with flying cars and neon lights, cyberpunk style, 3d rendered, dynamic camera movement, rain effects'">
                    🌆 Cyberpunk city with 3D flying cars and rain effects
                </div>
                <div style="padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px; cursor: pointer;" onclick="document.querySelector('textarea').value='Underwater coral reef with colorful fish and sea creatures, 3d rendered, volumetric lighting, smooth camera movement'">
                    🐠 3D underwater world with volumetric lighting
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px;">
                <strong>🔒 All generation happens offline on your local machine!</strong>
            </div>
        </div>
        """)
        
        # Event handlers
        generate_btn.click(
            fn=generator.generate_video_offline,
            inputs=[prompt_input, fps_choice, duration_slider, style_choice],
            outputs=[video_output, status_display]
        )
        
        prompt_input.submit(
            fn=generator.generate_video_offline,
            inputs=[prompt_input, fps_choice, duration_slider, style_choice],
            outputs=[video_output, status_display]
        )
    
    return app

if __name__ == "__main__":
    print("🔒 Starting Offline Text-to-Video Generator...")
    print("🌐 This system works completely offline!")
    print("📦 Installing required packages...")
    
    # Install required packages
    required_packages = ["gradio", "requests"]
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} already installed")
        except ImportError:
            print(f"📥 Installing {package}...")
            os.system(f"pip3 install {package}")
            print(f"✅ {package} installed successfully")
    
    app = create_offline_interface()
    
    print("\n🎉 Offline Video Generator ready!")
    print("🌐 Opening at: http://127.0.0.1:7860")
    print("🔧 ComfyUI backend: http://127.0.0.1:8188")
    print("🔒 100% OFFLINE OPERATION - No internet required!")
    print("\n🚀 Launching offline interface...")
    
    app.launch(
        server_name="127.0.0.1",
        server_port=7860,
        share=False,
        inbrowser=True,
        show_error=True
    )
