{"prompt": {"1": {"inputs": {"directory": "3d", "image_load_cap": 16, "skip_first_images": 0, "select_every_nth": 1}, "class_type": "VHS_LoadImages", "_meta": {"title": "Load Test Images"}}, "2": {"inputs": {"images": ["1", 0], "frame_rate": 8.0, "loop_count": 0, "filename_prefix": "WORKING_test_video", "format": "video/h264-mp4", "pingpong": false, "save_output": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Create Test Video"}}}, "client_id": "test123"}