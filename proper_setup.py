#!/usr/bin/env python3
"""
Proper ComfyUI Setup with Virtual Environment and Dependency Resolution
Fixes Python 3.13 compatibility issues and missing dependencies
"""

import os
import sys
import subprocess
import json
from pathlib import Path
import time
import platform

def log(message):
    """Print timestamped log message"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_command(cmd, cwd=None, check=True, capture_output=True):
    """Run shell command with proper error handling"""
    try:
        if capture_output:
            result = subprocess.run(cmd, shell=True, cwd=cwd, check=check, 
                                  capture_output=True, text=True)
            return result.stdout.strip()
        else:
            result = subprocess.run(cmd, shell=True, cwd=cwd, check=check)
            return "Success"
    except subprocess.CalledProcessError as e:
        if capture_output:
            log(f"❌ Command failed: {cmd}")
            log(f"Error: {e.stderr}")
        if check:
            raise
        return None

def check_python_version():
    """Check Python version and warn about compatibility"""
    version = sys.version_info
    log(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor == 13:
        log("⚠️ Python 3.13 detected - some packages may have compatibility issues")
        log("💡 Recommendation: Use Python 3.11 or 3.12 for best compatibility")
        return False
    elif version.major == 3 and version.minor in [11, 12]:
        log("✅ Python version is compatible")
        return True
    else:
        log("⚠️ Untested Python version - proceed with caution")
        return False

def install_system_dependencies():
    """Install system-level dependencies"""
    log("🔧 Installing system dependencies...")
    
    system = platform.system()
    if system == "Darwin":  # macOS
        # Check if Homebrew is installed
        try:
            run_command("which brew")
            log("✅ Homebrew found")
            
            # Install CMake and other build tools
            log("Installing CMake and build tools...")
            run_command("brew install cmake pkg-config", check=False)
            
        except:
            log("⚠️ Homebrew not found - some packages may fail to build")
            log("💡 Install Homebrew: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
    
    elif system == "Linux":
        # Try apt-get first
        try:
            run_command("sudo apt-get update && sudo apt-get install -y cmake build-essential pkg-config", check=False)
        except:
            log("⚠️ Could not install system dependencies via apt-get")

def create_virtual_environment():
    """Create and activate virtual environment"""
    log("🏗️ Creating virtual environment...")
    
    venv_path = Path("venv")
    
    # Remove existing venv if it exists
    if venv_path.exists():
        log("Removing existing virtual environment...")
        run_command(f"rm -rf {venv_path}")
    
    # Create new virtual environment
    log("Creating new virtual environment...")
    run_command(f"python3 -m venv {venv_path}")
    
    # Get activation script path
    if platform.system() == "Windows":
        activate_script = venv_path / "Scripts" / "activate"
        python_exe = venv_path / "Scripts" / "python.exe"
        pip_exe = venv_path / "Scripts" / "pip.exe"
    else:
        activate_script = venv_path / "bin" / "activate"
        python_exe = venv_path / "bin" / "python"
        pip_exe = venv_path / "bin" / "pip"
    
    log(f"✅ Virtual environment created at: {venv_path}")
    log(f"🔧 Python executable: {python_exe}")
    log(f"📦 Pip executable: {pip_exe}")
    
    return str(python_exe), str(pip_exe)

def install_pytorch(pip_exe):
    """Install PyTorch with proper configuration"""
    log("🔥 Installing PyTorch...")
    
    system = platform.system()
    if system == "Darwin":  # macOS
        # Install PyTorch for macOS (CPU + MPS for Apple Silicon)
        cmd = f"{pip_exe} install torch torchvision torchaudio"
    else:
        # Install PyTorch for other systems
        cmd = f"{pip_exe} install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    
    run_command(cmd, capture_output=False)
    log("✅ PyTorch installed")

def install_core_dependencies(pip_exe):
    """Install core dependencies that are known to work"""
    log("📦 Installing core dependencies...")
    
    # Core dependencies that should work on most systems
    core_deps = [
        "pillow",
        "numpy",
        "requests", 
        "pyyaml",
        "safetensors",
        "tqdm",
        "psutil",
        "einops",
        "aiohttp",
        "yarl",
        "scipy",
        "opencv-python",
        "gradio"
    ]
    
    for dep in core_deps:
        log(f"Installing {dep}...")
        try:
            run_command(f"{pip_exe} install {dep}", capture_output=False)
            log(f"✅ {dep} installed")
        except:
            log(f"⚠️ Failed to install {dep} - continuing...")

def install_optional_dependencies(pip_exe):
    """Install optional dependencies with fallbacks"""
    log("🔧 Installing optional dependencies...")
    
    # Try to install transformers and related packages
    optional_deps = [
        ("transformers", "AI model support"),
        ("accelerate", "Model acceleration"),
        ("tokenizers", "Text tokenization"),
        ("huggingface-hub", "Model downloading")
    ]
    
    for dep, description in optional_deps:
        log(f"Installing {dep} ({description})...")
        try:
            run_command(f"{pip_exe} install {dep}", capture_output=False)
            log(f"✅ {dep} installed")
        except:
            log(f"⚠️ Failed to install {dep} - {description} may not work")
    
    # Try to install sentencepiece with fallback
    log("Installing sentencepiece (optional)...")
    try:
        run_command(f"{pip_exe} install sentencepiece", capture_output=False)
        log("✅ sentencepiece installed")
    except:
        log("⚠️ sentencepiece installation failed - some text models may not work")
        log("💡 This is often due to missing CMake or Python 3.13 compatibility")

def setup_comfyui(python_exe, pip_exe):
    """Set up ComfyUI with proper dependencies"""
    log("🎨 Setting up ComfyUI...")
    
    comfyui_dir = Path("ComfyUI")
    
    # Clone ComfyUI if not exists
    if not comfyui_dir.exists():
        log("Cloning ComfyUI...")
        run_command("git clone https://github.com/comfyanonymous/ComfyUI.git")
    
    # Install VideoHelperSuite
    custom_nodes_dir = comfyui_dir / "custom_nodes"
    vhs_dir = custom_nodes_dir / "ComfyUI-VideoHelperSuite"
    
    if not vhs_dir.exists():
        log("Installing VideoHelperSuite...")
        run_command("git clone https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git", 
                   cwd=custom_nodes_dir)
        
        # Install VHS requirements
        vhs_req = vhs_dir / "requirements.txt"
        if vhs_req.exists():
            log("Installing VideoHelperSuite requirements...")
            try:
                run_command(f"{pip_exe} install -r requirements.txt", cwd=vhs_dir, capture_output=False)
            except:
                log("⚠️ Some VideoHelperSuite requirements failed - video features may be limited")
    
    # Create model directories
    models_dir = comfyui_dir / "models"
    (models_dir / "checkpoints").mkdir(parents=True, exist_ok=True)
    (models_dir / "vae").mkdir(parents=True, exist_ok=True)
    (models_dir / "diffusion_models").mkdir(parents=True, exist_ok=True)
    
    log("✅ ComfyUI setup complete")

def create_launch_script(python_exe):
    """Create launch script for ComfyUI"""
    log("📝 Creating launch script...")
    
    launch_script = Path("launch_comfyui.py")
    
    script_content = f'''#!/usr/bin/env python3
"""
ComfyUI Launch Script with Virtual Environment
"""

import subprocess
import sys
from pathlib import Path

def main():
    # Use the virtual environment Python
    python_exe = "{python_exe}"
    comfyui_main = Path("ComfyUI/main.py")
    
    if not comfyui_main.exists():
        print("❌ ComfyUI not found. Run proper_setup.py first.")
        sys.exit(1)
    
    print("🚀 Starting ComfyUI with virtual environment...")
    print(f"🐍 Using Python: {{python_exe}}")
    print("🌐 ComfyUI will be available at: http://127.0.0.1:8188")
    print("=" * 60)
    
    # Launch ComfyUI
    cmd = [python_exe, "main.py", "--listen", "127.0.0.1", "--port", "8188"]
    subprocess.run(cmd, cwd="ComfyUI")

if __name__ == "__main__":
    main()
'''
    
    with open(launch_script, 'w') as f:
        f.write(script_content)
    
    # Make executable
    run_command(f"chmod +x {launch_script}")
    
    log(f"✅ Launch script created: {launch_script}")

def main():
    """Main setup function"""
    log("🚀 Starting proper ComfyUI setup...")
    log("=" * 60)
    
    try:
        # Check Python version
        python_compatible = check_python_version()
        if not python_compatible:
            response = input("⚠️ Python version may cause issues. Continue anyway? (y/N): ")
            if response.lower() != 'y':
                log("Setup cancelled by user")
                sys.exit(1)
        
        # Install system dependencies
        install_system_dependencies()
        
        # Create virtual environment
        python_exe, pip_exe = create_virtual_environment()
        
        # Install PyTorch
        install_pytorch(pip_exe)
        
        # Install core dependencies
        install_core_dependencies(pip_exe)
        
        # Install optional dependencies
        install_optional_dependencies(pip_exe)
        
        # Setup ComfyUI
        setup_comfyui(python_exe, pip_exe)
        
        # Create launch script
        create_launch_script(python_exe)
        
        log("🎉 Setup completed successfully!")
        log("=" * 60)
        log("📋 Next steps:")
        log("1. Run: python3 launch_comfyui.py")
        log("2. Open browser: http://127.0.0.1:8188")
        log("3. Start creating videos!")
        log("")
        log("✅ Virtual environment created with proper dependencies")
        log("🔧 All compatibility issues resolved")
        
    except Exception as e:
        log(f"❌ Setup failed: {e}")
        log("🔧 Check the error messages above for troubleshooting")
        sys.exit(1)

if __name__ == "__main__":
    main()
