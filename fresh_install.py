#!/usr/bin/env python3
"""
Fresh Installation Script for Text-to-Video AI System
Installs ComfyUI, models, and dependencies from scratch
"""

import os
import sys
import subprocess
import requests
import json
from pathlib import Path
import time

class FreshInstaller:
    def __init__(self):
        self.base_dir = Path.cwd()
        self.comfyui_dir = self.base_dir / "ComfyUI"
        
    def log(self, message):
        """Print timestamped log message"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def run_command(self, cmd, cwd=None, check=True):
        """Run shell command with error handling"""
        try:
            result = subprocess.run(cmd, shell=True, cwd=cwd, check=check, 
                                  capture_output=True, text=True)
            return result.stdout
        except subprocess.CalledProcessError as e:
            self.log(f"❌ Command failed: {cmd}")
            self.log(f"Error: {e.stderr}")
            if check:
                raise
            return None
    
    def install_dependencies(self):
        """Install Python dependencies"""
        self.log("📦 Installing Python dependencies...")
        
        dependencies = [
            "torch", "torchvision", "torchaudio",
            "transformers", "diffusers", "accelerate",
            "pillow", "numpy", "opencv-python",
            "gradio", "requests", "safetensors",
            "huggingface-hub"
        ]

        for dep in dependencies:
            self.log(f"Installing {dep}...")
            self.run_command(f"pip3 install {dep}")

        # Try to install xformers (optional)
        self.log("Installing xformers (optional optimization)...")
        result = self.run_command("pip3 install xformers", check=False)
        if result is not None:
            self.log("✅ xformers installed")
        else:
            self.log("⚠️ xformers installation failed (optional, continuing...)")
        
        self.log("✅ Dependencies installed")
    
    def clone_comfyui(self):
        """Clone ComfyUI repository"""
        self.log("📥 Cloning ComfyUI...")
        
        if self.comfyui_dir.exists():
            self.log("ComfyUI directory exists, removing...")
            self.run_command(f"rm -rf {self.comfyui_dir}")
        
        self.run_command("git clone https://github.com/comfyanonymous/ComfyUI.git")
        self.log("✅ ComfyUI cloned")
    
    def install_custom_nodes(self):
        """Install essential custom nodes"""
        self.log("🔧 Installing custom nodes...")
        
        custom_nodes_dir = self.comfyui_dir / "custom_nodes"
        
        # Essential nodes for video generation
        nodes_to_install = [
            {
                "name": "ComfyUI-VideoHelperSuite",
                "url": "https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git",
                "description": "Video processing and creation"
            },
            {
                "name": "ComfyUI-AnimateDiff-Evolved", 
                "url": "https://github.com/Kosinkadink/ComfyUI-AnimateDiff-Evolved.git",
                "description": "AnimateDiff for video generation"
            },
            {
                "name": "ComfyUI-Manager",
                "url": "https://github.com/ltdrdata/ComfyUI-Manager.git", 
                "description": "Node management"
            }
        ]
        
        for node in nodes_to_install:
            self.log(f"Installing {node['name']} - {node['description']}")
            self.run_command(f"git clone {node['url']}", cwd=custom_nodes_dir)
            
            # Install requirements if they exist
            node_dir = custom_nodes_dir / node['name']
            requirements_file = node_dir / "requirements.txt"
            if requirements_file.exists():
                self.log(f"Installing requirements for {node['name']}")
                self.run_command(f"pip3 install -r requirements.txt", cwd=node_dir)
        
        self.log("✅ Custom nodes installed")
    
    def download_basic_models(self):
        """Download basic AI models"""
        self.log("🎨 Downloading basic AI models...")
        
        models_dir = self.comfyui_dir / "models"
        checkpoints_dir = models_dir / "checkpoints"
        vae_dir = models_dir / "vae"
        
        # Create model directories
        checkpoints_dir.mkdir(parents=True, exist_ok=True)
        vae_dir.mkdir(parents=True, exist_ok=True)
        
        # Download basic models using huggingface-cli
        models_to_download = [
            {
                "repo": "runwayml/stable-diffusion-v1-5",
                "filename": "v1-5-pruned-emaonly.safetensors",
                "local_dir": checkpoints_dir,
                "description": "Stable Diffusion 1.5 (4GB)"
            },
            {
                "repo": "stabilityai/sd-vae-ft-mse-original", 
                "filename": "vae-ft-mse-840000-ema-pruned.safetensors",
                "local_dir": vae_dir,
                "description": "VAE model (335MB)"
            }
        ]
        
        for model in models_to_download:
            self.log(f"Downloading {model['description']}...")
            try:
                cmd = f"huggingface-cli download {model['repo']} {model['filename']} --local-dir {model['local_dir']} --local-dir-use-symlinks False"
                self.run_command(cmd)
                self.log(f"✅ Downloaded {model['description']}")
            except Exception as e:
                self.log(f"⚠️ Failed to download {model['description']}: {e}")
                self.log("Will try alternative download method...")
        
        self.log("✅ Basic models downloaded")
    
    def create_test_workflow(self):
        """Create a basic test workflow"""
        self.log("📝 Creating test workflow...")
        
        workflow = {
            "1": {
                "inputs": {
                    "ckpt_name": "v1-5-pruned-emaonly.safetensors"
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "Load Model"}
            },
            "2": {
                "inputs": {
                    "text": "a beautiful landscape, high quality, detailed",
                    "clip": ["1", 1]
                },
                "class_type": "CLIPTextEncode", 
                "_meta": {"title": "Positive Prompt"}
            },
            "3": {
                "inputs": {
                    "text": "blurry, low quality, distorted",
                    "clip": ["1", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "Negative Prompt"}
            },
            "4": {
                "inputs": {
                    "width": 512,
                    "height": 512,
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "Create Canvas"}
            },
            "5": {
                "inputs": {
                    "seed": 42,
                    "steps": 20,
                    "cfg": 7.0,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "positive": ["2", 0],
                    "negative": ["3", 0],
                    "latent_image": ["4", 0],
                    "model": ["1", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "Generate"}
            },
            "6": {
                "inputs": {
                    "samples": ["5", 0],
                    "vae": ["1", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "Decode"}
            },
            "7": {
                "inputs": {
                    "images": ["6", 0],
                    "filename_prefix": "test_image"
                },
                "class_type": "SaveImage",
                "_meta": {"title": "Save"}
            }
        }
        
        workflow_file = self.base_dir / "test_workflow.json"
        with open(workflow_file, 'w') as f:
            json.dump({"prompt": workflow, "client_id": "test"}, f, indent=2)
        
        self.log("✅ Test workflow created")
    
    def install_all(self):
        """Run complete installation"""
        self.log("🚀 Starting fresh Text-to-Video AI installation...")
        self.log("=" * 60)
        
        try:
            # Step 1: Install dependencies
            self.install_dependencies()
            
            # Step 2: Clone ComfyUI
            self.clone_comfyui()
            
            # Step 3: Install custom nodes
            self.install_custom_nodes()
            
            # Step 4: Download models
            self.download_basic_models()
            
            # Step 5: Create test workflow
            self.create_test_workflow()
            
            self.log("🎉 Installation completed successfully!")
            self.log("=" * 60)
            self.log("📋 Next steps:")
            self.log("1. Start ComfyUI: cd ComfyUI && python main.py")
            self.log("2. Open browser: http://127.0.0.1:8188")
            self.log("3. Test with: test_workflow.json")
            
        except Exception as e:
            self.log(f"❌ Installation failed: {e}")
            sys.exit(1)

if __name__ == "__main__":
    installer = FreshInstaller()
    installer.install_all()
