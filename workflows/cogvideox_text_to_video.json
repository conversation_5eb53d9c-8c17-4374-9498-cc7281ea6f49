{"1": {"inputs": {"prompt": "A majestic dragon flying through a mystical forest with glowing particles, cinematic camera movement, volumetric lighting, photorealistic 3D rendering", "negative_prompt": "blurry, low quality, distorted, watermark, text, bad anatomy, static image, 2d flat, boring, simple", "num_frames": 96, "height": 480, "width": 720, "num_inference_steps": 50, "guidance_scale": 6.0, "fps": 24, "seed": 42}, "class_type": "CogVideoXSampler", "_meta": {"title": "CogVideoX Text-to-Video Generator"}}, "2": {"inputs": {"filename_prefix": "cogvideox_3d_video", "fps": 24, "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": ["1", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Save 3D Video Output"}}, "3": {"inputs": {"text": "Enhanced prompt for 3D visual generation", "speak_text": false}, "class_type": "ShowText|pysssss", "_meta": {"title": "Prompt Display"}}}