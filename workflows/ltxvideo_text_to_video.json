{"1": {"inputs": {"prompt": "A majestic dragon flying through a mystical forest with glowing particles, cinematic camera movement, volumetric lighting, photorealistic 3D rendering", "negative_prompt": "blurry, low quality, distorted, watermark, text, bad anatomy, static image, 2d flat, boring, simple", "width": 768, "height": 512, "num_frames": 96, "fps": 24, "motion_bucket_id": 127, "noise_aug_strength": 0.02, "seed": 42, "steps": 30, "cfg": 7.0}, "class_type": "LTXVBaseSampler", "_meta": {"title": "LTXVideo Text-to-Video Generator"}}, "2": {"inputs": {"prompt": "A majestic dragon flying through a mystical forest with glowing particles, cinematic camera movement, volumetric lighting, photorealistic 3D rendering, enhanced details, dynamic motion", "strength": 0.8, "seed": 42}, "class_type": "LTXVPromptEnhancer", "_meta": {"title": "Enhance Prompt for 3D Visuals"}}, "3": {"inputs": {"filename_prefix": "ltxvideo_3d_video", "fps": 24, "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": ["1", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Save 3D Video Output"}}, "4": {"inputs": {"images": ["1", 0], "grain_power": 0.1, "grain_scale": 1.0, "grain_sat": 1.0, "shadows": 0.0, "highs": 0.0, "gamma": 1.0, "seed": 42}, "class_type": "LTXVFilmGrain", "_meta": {"title": "Add Cinematic Film Grain"}}}