{"1": {"inputs": {"width": 512, "height": 512, "batch_size": 16}, "class_type": "EmptyLatentImage", "_meta": {"title": "Create Latent <PERSON>"}}, "2": {"inputs": {"text": "A majestic dragon flying through a mystical forest with glowing particles, cinematic camera movement, volumetric lighting, photorealistic 3D rendering, high quality, detailed, fantasy art", "clip": ["4", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Encode Positive Prompt"}}, "3": {"inputs": {"text": "blurry, low quality, distorted, watermark, text, bad anatomy, static image, 2d flat, boring, simple", "clip": ["4", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Encode Negative Prompt"}}, "4": {"inputs": {"ckpt_name": "sd_xl_base_1.0.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"seed": 42, "steps": 20, "cfg": 7.0, "sampler_name": "euler", "scheduler": "normal", "positive": ["2", 0], "negative": ["3", 0], "latent_image": ["1", 0], "model": ["4", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "Generate Images"}}, "6": {"inputs": {"samples": ["5", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "Decode to Images"}}, "7": {"inputs": {"images": ["6", 0], "frame_rate": 8.0, "loop_count": 0, "filename_prefix": "simple_test_video", "format": "video/h264-mp4", "pingpong": false, "save_output": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Create Video"}}}