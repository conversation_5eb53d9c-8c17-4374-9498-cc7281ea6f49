#!/usr/bin/env python3
"""
Install basic AI models that work with 8GB RAM systems
"""

import os
import requests
import subprocess
from pathlib import Path

def download_file(url, filepath):
    """Download a file with progress"""
    print(f"📥 Downloading {filepath.name}...")
    
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    
    with open(filepath, 'wb') as f:
        downloaded = 0
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                f.write(chunk)
                downloaded += len(chunk)
                if total_size > 0:
                    percent = (downloaded / total_size) * 100
                    print(f"\r📊 Progress: {percent:.1f}%", end='', flush=True)
    
    print(f"\n✅ Downloaded {filepath.name}")

def install_basic_models():
    """Install basic models for text-to-image generation"""
    
    print("🎬 Installing Basic AI Models for 8GB RAM Systems...")
    print("=" * 60)
    
    # Create model directories
    models_dir = Path("ComfyUI/models")
    checkpoints_dir = models_dir / "checkpoints"
    vae_dir = models_dir / "vae"
    
    checkpoints_dir.mkdir(parents=True, exist_ok=True)
    vae_dir.mkdir(parents=True, exist_ok=True)
    
    # Basic models that work with 8GB RAM
    models_to_download = [
        {
            "name": "Stable Diffusion 1.5 (Optimized)",
            "url": "https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.safetensors",
            "path": checkpoints_dir / "sd15_optimized.safetensors",
            "size": "~4GB"
        },
        {
            "name": "VAE for SD 1.5",
            "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors",
            "path": vae_dir / "vae-ft-mse-840000-ema-pruned.safetensors",
            "size": "~335MB"
        }
    ]
    
    for model in models_to_download:
        if model["path"].exists():
            print(f"✅ {model['name']} already exists")
            continue
            
        print(f"\n🎨 Downloading {model['name']} ({model['size']})...")
        try:
            download_file(model["url"], model["path"])
        except Exception as e:
            print(f"❌ Failed to download {model['name']}: {e}")
            print("🔄 Trying alternative method...")
            
            # Try using huggingface-cli
            try:
                repo_id = model["url"].split("/resolve/")[0].split("huggingface.co/")[1]
                filename = model["url"].split("/")[-1]
                
                cmd = [
                    "huggingface-cli", "download", repo_id, filename,
                    "--local-dir", str(model["path"].parent),
                    "--local-dir-use-symlinks", "False"
                ]
                
                subprocess.run(cmd, check=True)
                
                # Move file to correct name if needed
                downloaded_file = model["path"].parent / filename
                if downloaded_file.exists() and downloaded_file != model["path"]:
                    downloaded_file.rename(model["path"])
                
                print(f"✅ {model['name']} downloaded successfully!")
                
            except Exception as e2:
                print(f"❌ Alternative download also failed: {e2}")
                print(f"⚠️ You may need to download {model['name']} manually")
    
    print("\n🎉 Basic model installation complete!")
    print("✅ Your system now has AI models for text-to-image generation")
    print("🎬 These models will be used to create actual visual content from prompts")

if __name__ == "__main__":
    install_basic_models()
