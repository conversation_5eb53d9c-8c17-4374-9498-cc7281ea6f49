#!/usr/bin/env python3
"""
Test the working video generator to prove it creates real videos
"""

import sys
import os
sys.path.append('.')

from working_offline_generator import WorkingVideoGenerator
import time

def test_video_generation():
    """Test video generation with a simple prompt"""
    
    print("🎬 Testing WORKING Video Generator...")
    print("=" * 50)
    
    # Create generator
    generator = WorkingVideoGenerator()
    
    # Test prompt
    test_prompt = "A majestic dragon flying through a mystical forest with glowing particles"
    
    print(f"📝 Test prompt: {test_prompt}")
    print("🔄 Starting generation...")
    
    # Generate video
    for video_path, status in generator.generate_real_video(test_prompt, fps=8, duration=2, style="cinematic"):
        print(f"Status: {status}")
        if video_path:
            print(f"🎉 VIDEO GENERATED: {video_path}")
            
            # Check if file exists
            if os.path.exists(video_path):
                file_size = os.path.getsize(video_path)
                print(f"✅ File exists: {video_path}")
                print(f"📊 File size: {file_size} bytes")
                
                if file_size > 1000:  # At least 1KB
                    print("🎉 SUCCESS: Real video file generated!")
                    return True
                else:
                    print("⚠️ File too small, might be empty")
                    return False
            else:
                print("❌ File not found")
                return False
    
    print("❌ No video generated")
    return False

if __name__ == "__main__":
    success = test_video_generation()
    if success:
        print("\n🎉 WORKING VIDEO GENERATOR CONFIRMED!")
        print("✅ The system generates real video files!")
    else:
        print("\n❌ Test failed")
        print("🔧 Check ComfyUI server and try again")
