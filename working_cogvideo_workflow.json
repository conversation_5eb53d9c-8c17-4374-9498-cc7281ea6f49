{"1": {"inputs": {"model_path": "CogVideoX-5b"}, "class_type": "CogVideoXModelLoader", "_meta": {"title": "Load CogVideoX Model"}}, "2": {"inputs": {"vae_path": "CogVideoX-5b"}, "class_type": "CogVideoXVAELoader", "_meta": {"title": "Load CogVideoX VAE"}}, "3": {"inputs": {"text": "A majestic dragon flying through a mystical forest with glowing particles, cinematic camera movement, volumetric lighting, photorealistic 3D rendering, high quality, detailed", "model": ["1", 0]}, "class_type": "CogVideoTextEncode", "_meta": {"title": "Encode Positive Prompt"}}, "4": {"inputs": {"text": "blurry, low quality, distorted, watermark, text, bad anatomy, static image, 2d flat, boring, simple", "model": ["1", 0]}, "class_type": "CogVideoTextEncode", "_meta": {"title": "Encode Negative Prompt"}}, "5": {"inputs": {"model": ["1", 0], "positive": ["3", 0], "negative": ["4", 0], "num_frames": 49, "steps": 50, "cfg": 6.0, "seed": 42, "scheduler": "CogVideoXDDIM"}, "class_type": "CogVideoSampler", "_meta": {"title": "Generate Video Latents"}}, "6": {"inputs": {"samples": ["5", 0], "vae": ["2", 0]}, "class_type": "CogVideoDecode", "_meta": {"title": "Decode Video"}}, "7": {"inputs": {"images": ["6", 0], "frame_rate": 24.0, "loop_count": 0, "filename_prefix": "cogvideox_3d_video", "format": "video/h264-mp4", "pingpong": false, "save_output": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Save 3D Video Output"}}}