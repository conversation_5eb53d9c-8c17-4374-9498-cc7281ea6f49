#!/bin/bash
# Offline Text-to-Video Generator Launcher
# Ensures complete offline operation without internet dependency

cd "$(dirname "$0")"

echo "🔒 Offline Text-to-Video Generator"
echo "=================================="
echo "🌐 100% OFFLINE OPERATION"
echo ""

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not found"
    echo "Please install Python 3 and try again"
    exit 1
fi

# Check if ComfyUI is available
if [ ! -d "ComfyUI" ]; then
    echo "❌ ComfyUI directory not found"
    echo "Please make sure ComfyUI is installed in this directory"
    exit 1
fi

# Install required Python packages (offline compatible)
echo "📦 Installing required packages for offline operation..."
python3 -m pip install gradio requests --quiet

# Check offline dependencies
echo ""
echo "🔍 Checking offline dependencies..."

# Check if VideoHelperSuite is installed
if [ -d "ComfyUI/custom_nodes/ComfyUI-VideoHelperSuite" ]; then
    echo "✅ VideoHelperSuite node available"
else
    echo "⚠️  VideoHelperSuite node missing - video saving may not work"
fi

# Check if basic models exist
if [ -d "ComfyUI/models/vae" ] && [ "$(ls -A ComfyUI/models/vae)" ]; then
    echo "✅ VAE models found"
else
    echo "⚠️  VAE models missing - may affect video quality"
fi

# Check if ComfyUI is running
echo ""
echo "🔍 Checking ComfyUI server..."
if curl -s http://127.0.0.1:8188/ > /dev/null 2>&1; then
    echo "✅ ComfyUI server is running"
else
    echo "⚠️  ComfyUI server not detected"
    echo "🚀 Starting ComfyUI server for offline operation..."

    # Start ComfyUI in background
    cd ComfyUI
    python3 main.py --listen 127.0.0.1 --port 8188 > ../comfyui.log 2>&1 &
    COMFYUI_PID=$!
    cd ..

    echo "⏳ Waiting for ComfyUI to start..."
    for i in {1..30}; do
        if curl -s http://127.0.0.1:8188/ > /dev/null 2>&1; then
            echo "✅ ComfyUI server started successfully!"
            break
        fi
        sleep 2
        echo "   Waiting... ($i/30)"
    done

    if ! curl -s http://127.0.0.1:8188/ > /dev/null 2>&1; then
        echo "❌ Failed to start ComfyUI server"
        echo "Please start ComfyUI manually and try again"
        exit 1
    fi
fi

# Validate offline operation
echo ""
echo "🔒 Validating offline operation..."
echo "✅ No internet connection required"
echo "✅ All processing happens locally"
echo "✅ AI models run on your machine"
echo "✅ Generated videos saved locally"

# Launch the offline video generator interface
echo ""
echo "🔒 Launching Offline Video Generator Interface..."
echo "🌐 Interface will open at: http://127.0.0.1:7860"
echo "🔧 ComfyUI backend running at: http://127.0.0.1:8188"
echo "🔒 100% OFFLINE - No internet required!"
echo ""
echo "✨ Ready to generate REAL 3D videos completely offline!"
echo ""

python3 offline_video_generator.py

echo ""
echo "👋 Offline video generator closed"
