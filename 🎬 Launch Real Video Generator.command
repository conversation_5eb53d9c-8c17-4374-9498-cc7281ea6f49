#!/bin/bash
# Real Text-to-Video Generator Launcher
# Automatically sets up models and launches the interface

cd "$(dirname "$0")"

echo "🎬 Real Text-to-Video Generator"
echo "================================"
echo ""

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not found"
    echo "Please install Python 3 and try again"
    exit 1
fi

# Check if ComfyUI is available
if [ ! -d "ComfyUI" ]; then
    echo "❌ ComfyUI directory not found"
    echo "Please make sure ComfyUI is installed in this directory"
    exit 1
fi

# Install required Python packages
echo "📦 Installing required packages..."
python3 -m pip install gradio requests websocket-client huggingface-hub[cli] --quiet

# Check and download models
echo ""
echo "🤖 Checking AI models..."
python3 model_downloader.py

# Check if ComfyUI is running
echo ""
echo "🔍 Checking ComfyUI server..."
if curl -s http://127.0.0.1:8188/ > /dev/null 2>&1; then
    echo "✅ ComfyUI server is running"
else
    echo "⚠️  ComfyUI server not detected"
    echo "🚀 Starting ComfyUI server..."
    
    # Start ComfyUI in background
    cd ComfyUI
    python3 main.py --listen 127.0.0.1 --port 8188 > ../comfyui.log 2>&1 &
    COMFYUI_PID=$!
    cd ..
    
    echo "⏳ Waiting for ComfyUI to start..."
    for i in {1..30}; do
        if curl -s http://127.0.0.1:8188/ > /dev/null 2>&1; then
            echo "✅ ComfyUI server started successfully!"
            break
        fi
        sleep 2
        echo "   Waiting... ($i/30)"
    done
    
    if ! curl -s http://127.0.0.1:8188/ > /dev/null 2>&1; then
        echo "❌ Failed to start ComfyUI server"
        echo "Please start ComfyUI manually and try again"
        exit 1
    fi
fi

# Launch the real video generator interface
echo ""
echo "🎬 Launching Real Video Generator Interface..."
echo "🌐 Interface will open at: http://127.0.0.1:7860"
echo "🔧 ComfyUI backend running at: http://127.0.0.1:8188"
echo ""
echo "✨ Ready to generate REAL 3D videos from text prompts!"
echo ""

python3 real_text_to_video.py

echo ""
echo "👋 Video generator closed"
