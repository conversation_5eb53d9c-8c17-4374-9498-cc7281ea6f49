{"prompt": {"1": {"inputs": {"width": 512, "height": 512, "batch_size": 16}, "class_type": "EmptyLatentImage", "_meta": {"title": "Create Latent <PERSON>"}}, "2": {"inputs": {"samples": ["1", 0], "vae": ["3", 0]}, "class_type": "VAEDecode", "_meta": {"title": "Decode to Images"}}, "3": {"inputs": {"vae_name": "wan_2.1_vae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "4": {"inputs": {"images": ["2", 0], "frame_rate": 8.0, "loop_count": 0, "filename_prefix": "minimal_test_video", "format": "video/h264-mp4", "pingpong": false, "save_output": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Create Video"}}}, "client_id": "test123"}