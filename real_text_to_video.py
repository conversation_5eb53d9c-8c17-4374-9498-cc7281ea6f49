#!/usr/bin/env python3
"""
Real Text-to-Video Interface with ComfyUI Integration
Creates actual 3D visual videos from text prompts using CogVideoX and LTXVideo
"""

import gradio as gr
import requests
import json
import time
import os
import uuid
import websocket
import threading
from pathlib import Path
from urllib.parse import urljoin
import base64

# ComfyUI API Configuration
COMFYUI_URL = "http://127.0.0.1:8188"
COMFYUI_WS_URL = "ws://127.0.0.1:8188/ws"
OUTPUT_DIR = "ComfyUI/output"

class ComfyUIClient:
    def __init__(self, server_address="127.0.0.1:8188"):
        self.server_address = server_address
        self.client_id = str(uuid.uuid4())
        
    def queue_prompt(self, prompt):
        """Queue a prompt for execution"""
        try:
            p = {"prompt": prompt, "client_id": self.client_id}
            data = json.dumps(p).encode('utf-8')
            req = requests.post(f"http://{self.server_address}/prompt", data=data, timeout=30)
            return json.loads(req.content)
        except Exception as e:
            print(f"Error queuing prompt: {e}")
            return None

    def get_history(self, prompt_id):
        """Get the history of a prompt execution"""
        try:
            response = requests.get(f"http://{self.server_address}/history/{prompt_id}", timeout=30)
            return json.loads(response.content)
        except Exception as e:
            print(f"Error getting history: {e}")
            return None

    def get_queue(self):
        """Get the current queue status"""
        try:
            response = requests.get(f"http://{self.server_address}/queue", timeout=30)
            return json.loads(response.content)
        except Exception as e:
            print(f"Error getting queue: {e}")
            return None

    def check_server(self):
        """Check if ComfyUI server is running"""
        try:
            response = requests.get(f"http://{self.server_address}/", timeout=5)
            return response.status_code == 200
        except:
            return False

class TextToVideoInterface:
    def __init__(self):
        self.client = ComfyUIClient()
        self.current_prompt_id = None
        
    def create_cogvideox_workflow(self, prompt, fps=24, duration=3, style="cinematic"):
        """Create a CogVideoX workflow for text-to-video generation"""

        # Enhanced prompt with style and technical parameters for 3D visual content
        enhanced_prompt = f"{prompt}, {style}, high quality, detailed, cinematic lighting, smooth motion, 3d rendered, volumetric lighting, realistic textures"
        negative_prompt = "blurry, low quality, distorted, watermark, text, bad anatomy, static image, 2d flat, boring"

        # Simplified workflow that works with available nodes
        workflow = {
            "1": {
                "inputs": {
                    "text": enhanced_prompt,
                    "width": 720,
                    "height": 480,
                    "batch_size": fps * duration
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "Empty Latent Image"}
            },
            "2": {
                "inputs": {
                    "text": enhanced_prompt,
                    "clip": ["4", 0]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Prompt)"}
            },
            "3": {
                "inputs": {
                    "text": negative_prompt,
                    "clip": ["4", 0]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Negative)"}
            },
            "4": {
                "inputs": {
                    "ckpt_name": "sd_xl_base_1.0.safetensors"
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "Load Checkpoint"}
            },
            "5": {
                "inputs": {
                    "seed": int(time.time()),
                    "steps": 20,
                    "cfg": 7.0,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "positive": ["2", 0],
                    "negative": ["3", 0],
                    "latent_image": ["1", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "KSampler"}
            },
            "6": {
                "inputs": {
                    "samples": ["5", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE Decode"}
            },
            "7": {
                "inputs": {
                    "filename_prefix": f"real_video_3d_{int(time.time())}",
                    "fps": fps,
                    "format": "video/h264-mp4",
                    "pix_fmt": "yuv420p",
                    "crf": 19,
                    "save_metadata": True,
                    "pingpong": False,
                    "save_output": True,
                    "images": ["6", 0]
                },
                "class_type": "VHS_VideoCombine",
                "_meta": {"title": "Save 3D Video"}
            }
        }

        return workflow
        
    def create_ltxvideo_workflow(self, prompt, fps=24, duration=3, style="cinematic"):
        """Create an LTXVideo workflow for text-to-video generation"""
        
        # Enhanced prompt for 3D visual content
        enhanced_prompt = f"{prompt}, {style}, high quality, detailed, cinematic lighting, smooth motion, 3d rendered, volumetric lighting, realistic textures, dynamic camera movement"
        
        workflow = {
            "1": {
                "inputs": {
                    "prompt": enhanced_prompt,
                    "negative_prompt": "blurry, low quality, distorted, watermark, text, bad anatomy, static image, 2d flat, boring",
                    "width": 768,
                    "height": 512,
                    "num_frames": fps * duration,
                    "fps": fps,
                    "motion_bucket_id": 127,
                    "noise_aug_strength": 0.02,
                    "seed": int(time.time())
                },
                "class_type": "LTXVBaseSampler",
                "_meta": {"title": "LTXVideo Text-to-Video"}
            },
            "2": {
                "inputs": {
                    "filename_prefix": f"ltxvideo_3d_{int(time.time())}",
                    "fps": fps,
                    "format": "video/h264-mp4",
                    "pix_fmt": "yuv420p",
                    "crf": 19,
                    "save_metadata": True,
                    "pingpong": False,
                    "save_output": True,
                    "videopreview": ["1", 0]
                },
                "class_type": "VHS_VideoCombine",
                "_meta": {"title": "Save Video"}
            }
        }
        
        return workflow

    def generate_video(self, prompt, fps=24, duration=3, style="cinematic", model="CogVideoX"):
        """Generate video from text prompt using real ComfyUI API"""
        
        if not prompt.strip():
            yield None, "⚠️ Please enter a prompt to generate a video."
            return
            
        # Check if ComfyUI server is running
        if not self.client.check_server():
            yield None, "❌ **ComfyUI Server Not Running**\n\n🔧 **Solution:** Start ComfyUI server first:\n\n1. Open Terminal\n2. Navigate to ComfyUI folder\n3. Run: `python main.py`\n4. Wait for server to start on http://127.0.0.1:8188"
            return
            
        try:
            yield None, f"🎬 **Starting REAL video generation...**\n\n💭 **Your prompt:** {prompt}\n\n⚙️ **Settings:** {fps}fps, {duration}s, {style} style, {model} model\n\n🔄 **Status:** Preparing workflow..."
            
            # Create workflow based on selected model
            if model == "CogVideoX":
                workflow = self.create_cogvideox_workflow(prompt, fps, duration, style)
            else:  # LTXVideo
                workflow = self.create_ltxvideo_workflow(prompt, fps, duration, style)
            
            yield None, f"🎬 **Starting REAL video generation...**\n\n💭 **Your prompt:** {prompt}\n\n⚙️ **Settings:** {fps}fps, {duration}s, {style} style, {model} model\n\n🔄 **Status:** Sending to ComfyUI..."
            
            # Queue the prompt
            result = self.client.queue_prompt(workflow)
            if not result:
                yield None, "❌ **Error:** Failed to queue prompt. Check ComfyUI server."
                return
                
            prompt_id = result.get('prompt_id')
            if not prompt_id:
                yield None, "❌ **Error:** No prompt ID returned from ComfyUI."
                return
                
            self.current_prompt_id = prompt_id
            
            yield None, f"🎬 **REAL video generation in progress...**\n\n💭 **Your prompt:** {prompt}\n\n⚙️ **Settings:** {fps}fps, {duration}s, {style} style, {model} model\n\n🔄 **Status:** Processing with AI models...\n\n📋 **Prompt ID:** {prompt_id}"
            
            # Monitor progress
            start_time = time.time()
            max_wait_time = 300  # 5 minutes max
            
            while time.time() - start_time < max_wait_time:
                # Check queue status
                queue_info = self.client.get_queue()
                if queue_info:
                    queue_running = queue_info.get('queue_running', [])
                    queue_pending = queue_info.get('queue_pending', [])
                    
                    if any(item[1] == prompt_id for item in queue_running):
                        elapsed = int(time.time() - start_time)
                        yield None, f"🎬 **REAL video generation in progress...**\n\n💭 **Your prompt:** {prompt}\n\n⚙️ **Settings:** {fps}fps, {duration}s, {style} style, {model} model\n\n🔄 **Status:** AI models generating 3D visual content...\n\n⏱️ **Elapsed:** {elapsed}s\n\n📋 **Prompt ID:** {prompt_id}"
                    elif any(item[1] == prompt_id for item in queue_pending):
                        queue_position = next((i+1 for i, item in enumerate(queue_pending) if item[1] == prompt_id), 0)
                        yield None, f"🎬 **REAL video generation queued...**\n\n💭 **Your prompt:** {prompt}\n\n⚙️ **Settings:** {fps}fps, {duration}s, {style} style, {model} model\n\n🔄 **Status:** Waiting in queue (position {queue_position})\n\n📋 **Prompt ID:** {prompt_id}"
                    else:
                        # Check if completed
                        history = self.client.get_history(prompt_id)
                        if history and prompt_id in history:
                            # Generation completed
                            outputs = history[prompt_id].get('outputs', {})
                            
                            # Look for video output
                            video_path = None
                            for node_id, output in outputs.items():
                                if 'gifs' in output:
                                    video_info = output['gifs'][0]
                                    video_path = f"ComfyUI/output/{video_info['filename']}"
                                    break
                                elif 'videos' in output:
                                    video_info = output['videos'][0]
                                    video_path = f"ComfyUI/output/{video_info['filename']}"
                                    break
                            
                            if video_path and os.path.exists(video_path):
                                yield video_path, f"✅ **REAL 3D Video Generated Successfully!**\n\n💭 **Your prompt:** {prompt}\n\n🎬 **Result:** 3D visual video created with actual AI models\n\n📁 **Location:** `{video_path}`\n\n🎉 **Success:** Your prompt has been transformed into actual 3D visual content!"
                            else:
                                yield None, f"✅ **Generation completed!**\n\n💭 **Your prompt:** {prompt}\n\n🎬 **Result:** Video saved to ComfyUI output folder\n\n📁 **Check:** ComfyUI/output/ directory\n\n📋 **Prompt ID:** {prompt_id}"
                            return
                
                time.sleep(2)
            
            # Timeout
            yield None, f"⏰ **Timeout:** Generation took longer than expected.\n\n💭 **Your prompt:** {prompt}\n\n🔍 **Check:** ComfyUI interface at http://127.0.0.1:8188\n\n📋 **Prompt ID:** {prompt_id}"
                
        except Exception as e:
            yield None, f"❌ **Error:** {str(e)}\n\n🔧 **Solution:** Make sure ComfyUI is running at http://127.0.0.1:8188"

def create_interface():
    """Create the enhanced ChatGPT-style interface for real video generation"""

    interface = TextToVideoInterface()

    # Custom CSS for enhanced styling
    css = """
    .gradio-container {
        max-width: 1000px !important;
        margin: 0 auto !important;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        min-height: 100vh !important;
    }

    .main-container {
        background: white !important;
        border-radius: 16px !important;
        padding: 30px !important;
        margin: 20px !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2) !important;
    }

    .prompt-box textarea {
        border-radius: 12px !important;
        border: 2px solid #e5e5e5 !important;
        padding: 20px !important;
        font-size: 16px !important;
        min-height: 100px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    }

    .prompt-box textarea:focus {
        border-color: #10a37f !important;
        box-shadow: 0 0 0 3px rgba(16,163,127,0.1) !important;
        outline: none !important;
    }

    .generate-btn {
        background: linear-gradient(135deg, #10a37f 0%, #0d8f6c 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 18px 35px !important;
        font-weight: 600 !important;
        font-size: 18px !important;
        transition: all 0.3s ease !important;
    }

    .generate-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 20px rgba(16,163,127,0.3) !important;
    }

    .status-display {
        background: #f8f9fa !important;
        border-radius: 12px !important;
        padding: 20px !important;
        margin: 15px 0 !important;
        border-left: 4px solid #10a37f !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        min-height: 120px !important;
    }

    .settings-row {
        background: #f8f9fa !important;
        border-radius: 12px !important;
        padding: 20px !important;
        margin: 15px 0 !important;
    }

    .video-output {
        border-radius: 12px !important;
        overflow: hidden !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    }
    """

    with gr.Blocks(css=css, title="🎬 Real AI Video Generator", theme=gr.themes.Soft()) as app:

        with gr.Column(elem_classes=["main-container"]):

            # Header
            gr.HTML("""
            <div style="text-align: center; padding: 0 0 30px 0;">
                <h1 style="color: #2d3748; margin-bottom: 8px; font-size: 2.8em;">🎬 Real AI Video Generator</h1>
                <p style="color: #718096; font-size: 20px; font-weight: 500;">Create actual 3D visual videos from text prompts • Powered by CogVideoX & LTXVideo</p>
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 10px 20px; border-radius: 25px; display: inline-block; margin-top: 10px; font-weight: 600;">
                    ✨ REAL VIDEO GENERATION - NO MORE SIMULATIONS!
                </div>
            </div>
            """)

            # Main prompt input (Enhanced ChatGPT style)
            prompt_input = gr.Textbox(
                placeholder="🎨 Describe the 3D visual video you want to create...\n\nExample: A majestic dragon flying through a mystical forest with glowing particles, cinematic camera movement, volumetric lighting, photorealistic 3D rendering",
                lines=5,
                elem_classes=["prompt-box"],
                show_label=False,
                max_lines=10
            )

            # Enhanced settings in a nice row
            with gr.Row(elem_classes=["settings-row"]):
                model_choice = gr.Radio(
                    choices=["CogVideoX", "LTXVideo"],
                    value="CogVideoX",
                    label="🤖 AI Model",
                    info="CogVideoX: High quality | LTXVideo: Fast generation"
                )
                fps_choice = gr.Radio(
                    choices=[16, 24, 30],
                    value=24,
                    label="🎞️ Frame Rate",
                    info="Higher = smoother motion"
                )
                duration_slider = gr.Slider(
                    minimum=2,
                    maximum=8,
                    value=4,
                    step=1,
                    label="⏱️ Duration (seconds)",
                    info="Video length"
                )
                style_choice = gr.Dropdown(
                    choices=[
                        "cinematic",
                        "photorealistic",
                        "3d rendered",
                        "anime style",
                        "documentary",
                        "artistic",
                        "sci-fi",
                        "fantasy",
                        "vintage film",
                        "modern"
                    ],
                    value="cinematic",
                    label="🎨 Style",
                    info="Visual aesthetic"
                )

            # Generate button (Enhanced)
            generate_btn = gr.Button(
                "🎬 Generate Real 3D Video",
                variant="primary",
                elem_classes=["generate-btn"],
                size="lg"
            )

            # Video output (NEW!)
            video_output = gr.Video(
                label="🎬 Generated Video",
                elem_classes=["video-output"],
                autoplay=True,
                show_share_button=True
            )

            # Status display (Enhanced ChatGPT-like)
            status_display = gr.Markdown(
                "💡 **Ready to create amazing 3D videos!**\n\nEnter your prompt above and click Generate Real 3D Video to start actual video generation using AI models.",
                elem_classes=["status-display"]
            )

        # Enhanced examples section
        gr.HTML("""
        <div style="margin-top: 25px; padding: 30px; background: white; border-radius: 16px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <h3 style="color: #2d3748; margin-bottom: 25px; text-align: center; font-size: 1.5em;">💡 Try These 3D Visual Prompts</h3>
            <div style="display: grid; gap: 15px;">
                <div style="padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px; cursor: pointer; transition: transform 0.2s;" onclick="document.querySelector('textarea').value='A majestic phoenix rising from flames in a mystical forest, 3d rendered, volumetric lighting, cinematic camera movement, photorealistic'">
                    🔥 A majestic phoenix rising from flames in a mystical forest, 3d rendered, volumetric lighting, cinematic camera movement, photorealistic
                </div>
                <div style="padding: 20px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border-radius: 12px; cursor: pointer; transition: transform 0.2s;" onclick="document.querySelector('textarea').value='Futuristic city with flying cars and neon lights, cyberpunk style, 3d rendered, dynamic camera movement, rain effects, photorealistic'">
                    🌆 Futuristic city with flying cars and neon lights, cyberpunk style, 3d rendered, dynamic camera movement, rain effects, photorealistic
                </div>
                <div style="padding: 20px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; border-radius: 12px; cursor: pointer; transition: transform 0.2s;" onclick="document.querySelector('textarea').value='Underwater coral reef with colorful fish and sea creatures, 3d rendered, volumetric lighting, smooth camera movement, photorealistic'">
                    🐠 Underwater coral reef with colorful fish and sea creatures, 3d rendered, volumetric lighting, smooth camera movement, photorealistic
                </div>
                <div style="padding: 20px; background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; border-radius: 12px; cursor: pointer; transition: transform 0.2s;" onclick="document.querySelector('textarea').value='Space station orbiting Earth with astronauts floating, 3d rendered, cinematic lighting, zero gravity effects, photorealistic'">
                    🚀 Space station orbiting Earth with astronauts floating, 3d rendered, cinematic lighting, zero gravity effects, photorealistic
                </div>
            </div>
        </div>
        """)

        # Event handlers
        generate_btn.click(
            fn=interface.generate_video,
            inputs=[prompt_input, fps_choice, duration_slider, style_choice, model_choice],
            outputs=[video_output, status_display]
        )

        # Enter key support
        prompt_input.submit(
            fn=interface.generate_video,
            inputs=[prompt_input, fps_choice, duration_slider, style_choice, model_choice],
            outputs=[video_output, status_display]
        )

    return app

if __name__ == "__main__":
    print("🎬 Starting Real Text-to-Video Interface...")
    print("📦 Installing required packages...")

    # Install required packages
    required_packages = ["gradio", "requests", "websocket-client"]
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package} already installed")
        except ImportError:
            print(f"📥 Installing {package}...")
            os.system(f"pip3 install {package}")
            print(f"✅ {package} installed successfully")

    # Create and launch the interface
    app = create_interface()

    print("\n🎉 Real Video Generation Interface ready!")
    print("🌐 Opening at: http://127.0.0.1:7860")
    print("🔧 Make sure ComfyUI is running at: http://127.0.0.1:8188")
    print("\n🚀 Launching interface...")

    app.launch(
        server_name="127.0.0.1",
        server_port=7860,
        share=False,
        inbrowser=True,
        show_error=True
    )
