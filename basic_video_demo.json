{"1": {"inputs": {"width": 512, "height": 512, "batch_size": 16}, "class_type": "EmptyLatentImage", "_meta": {"title": "Create Video Frames"}}, "2": {"inputs": {"images": ["3", 0], "frame_rate": 8.0, "loop_count": 0, "filename_prefix": "demo_video", "format": "video/h264-mp4", "pingpong": false, "save_output": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Create Video 🎥🅥🅗🅢"}}, "3": {"inputs": {"image": "input.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}}