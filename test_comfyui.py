#!/usr/bin/env python3
"""
Test script to diagnose ComfyUI startup issues
"""

import sys
import os
import traceback

def test_imports():
    """Test all ComfyUI imports step by step"""
    print("🔍 Testing ComfyUI imports...")
    
    # Add ComfyUI to path
    comfyui_path = os.path.join(os.getcwd(), 'ComfyUI')
    sys.path.insert(0, comfyui_path)
    
    tests = [
        ('torch', 'PyTorch'),
        ('torchvision', 'TorchVision'),
        ('transformers', 'Transformers'),
        ('folder_paths', 'ComfyUI folder_paths'),
        ('model_management', 'ComfyUI model_management'),
        ('comfy.model_management', 'ComfyUI model management'),
        ('nodes', 'ComfyUI nodes'),
        ('execution', 'ComfyUI execution'),
        ('server', 'ComfyUI server'),
    ]
    
    for module, description in tests:
        try:
            print(f"  Testing {description}...")
            __import__(module)
            print(f"  ✅ {description} imported successfully")
        except Exception as e:
            print(f"  ❌ {description} failed: {e}")
            if 'torch' in module.lower():
                # Critical error
                print(f"  🚨 Critical dependency failed!")
                traceback.print_exc()
                return False
            else:
                # Non-critical, continue
                print(f"  ⚠️ Non-critical import failed, continuing...")
    
    return True

def test_comfyui_startup():
    """Test ComfyUI startup process"""
    print("\n🚀 Testing ComfyUI startup process...")
    
    try:
        # Change to ComfyUI directory
        os.chdir('ComfyUI')
        
        # Test basic startup
        print("  Testing main module import...")
        import main
        print("  ✅ Main module imported successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ ComfyUI startup failed: {e}")
        traceback.print_exc()
        return False

def main():
    print("🧪 ComfyUI Diagnostic Test")
    print("=" * 50)
    
    print(f"Python version: {sys.version}")
    print(f"Current directory: {os.getcwd()}")
    
    # Test imports
    if not test_imports():
        print("\n❌ Critical import test failed!")
        return 1
    
    # Test startup
    if not test_comfyui_startup():
        print("\n❌ ComfyUI startup test failed!")
        return 1
    
    print("\n🎉 All tests passed! ComfyUI should be working.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
